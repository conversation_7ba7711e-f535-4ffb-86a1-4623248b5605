{"name": "Webhook Expression Test", "nodes": [{"parameters": {"httpMethod": "POST", "path": "iav", "responseMode": "lastNode", "options": {}}, "name": "webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [500, 300], "webhookId": "f5a8a1c6-3e7b-4b1d-8a8e-9c9c3e2f9d2a"}, {"parameters": {"command": "=`echo \"SUCCESS: The URL passed to the shell is: ${$node[\"webhook\"].json[\"body\"][\"url\"]}\"`"}, "name": "echo_test", "type": "n8n-nodes-base.executeCommand", "typeVersion": 1, "position": [700, 300]}], "connections": {"webhook": {"main": [[{"node": "echo_test", "type": "main", "index": 0}]]}}, "settings": {}}